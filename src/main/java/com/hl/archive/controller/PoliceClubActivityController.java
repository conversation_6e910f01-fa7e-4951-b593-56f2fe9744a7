package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceClubActivityQueryDTO;
import com.hl.archive.domain.entity.PoliceClubActivity;
import com.hl.archive.service.PoliceClubActivityService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/clubActivity")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "社团活动")
public class PoliceClubActivityController {

    private final PoliceClubActivityService policeClubActivityService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<PoliceClubActivity>> page(@RequestBody PoliceClubActivityQueryDTO requestDTO) {
        Page<PoliceClubActivity> page = policeClubActivityService.pageList(requestDTO);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody PoliceClubActivity request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubActivityService.save(request));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody PoliceClubActivity request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubActivityService.updateById(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody PoliceClubActivity request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubActivityService.removeById(request));
    }


}
