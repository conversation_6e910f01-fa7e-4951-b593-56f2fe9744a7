package com.hl.archive.listener.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "extend-task.club-activity")
@Component
public class PoliceClubActivityConfig {

    private String configUuid;
    private String activityTime;
    private String location;
    private String participants;
    private String media;
    private String activityName;
    private String advancePayment;
    private String activityBudget;

    private String attachment;

    private String activityNotice;

    private String activityPlan;

}
