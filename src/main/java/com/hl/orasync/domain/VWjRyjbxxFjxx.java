package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "FJXX.V_WJ_RYJBXX")
@Data
@TableName(value = "FJXX.V_WJ_RYJBXX")
@AutoMapper(target = AuxiliaryPoliceInfo.class, uses = {ConversionUtils.class})
public class VWjRyjbxxFjxx {
    /**
     * 主键编号
     */
    @TableField(value = "PERSONID")
    @ApiModelProperty(value = "主键编号")
    private String personid;

    /**
     * 姓名
     */
    @TableField(value = "XM")
    @ApiModelProperty(value = "姓名")
    @AutoMapping(target = "name")
    private String xm;

    /**
     * 曾用名
     */
    @TableField(value = "CYM")
    @ApiModelProperty(value = "曾用名")
    @AutoMapping(target = "formerName")
    private String cym;

    /**
     * 单位名称
     */
    @TableField(value = "DWMC")
    @ApiModelProperty(value = "单位名称")
    @AutoMapping(target = "organization")
    private String dwmc;

    /**
     * 性别
     */
    @TableField(value = "XB")
    @ApiModelProperty(value = "性别")
    @AutoMapping(target = "gender")
    private String xb;

    /**
     * 层级
     */
    @TableField(value = "CJMC")
    @ApiModelProperty(value = "层级")
    @AutoMapping(target = "hierarchyLevel")
    private String cjmc;

    /**
     * 出生日期
     */
    @TableField(value = "CSRQ")
    @ApiModelProperty(value = "出生日期")
    @AutoMapping(target = "birthDate", qualifiedByName = "strToLocalDate")
    private String csrq;

    /**
     * 任职状态
     */
    @TableField(value = "RZZTMC")
    @ApiModelProperty(value = "任职状态")
    @AutoMapping(target = "employmentStatus")
    private String rzztmc;

    /**
     * 身份证号
     */
    @TableField(value = "GMSFHM")
    @ApiModelProperty(value = "身份证号")
    @AutoMapping(target = "idNumber")
    private String gmsfhm;

    /**
     * 参加辅警工作时间
     */
    @TableField(value = "FJGZRQ")
    @ApiModelProperty(value = "参加辅警工作时间")
    @AutoMapping(target = "startAuxiliaryDate", qualifiedByName = "strToLocalDate")
    private String fjgzrq;

    /**
     * 年龄
     */
    @TableField(value = "NL")
    @ApiModelProperty(value = "年龄")
    @AutoMapping(target = "age", qualifiedByName = "bigDecimalToString")
    private BigDecimal nl;

    /**
     * 岗位
     */
    @TableField(value = "GWMC")
    @ApiModelProperty(value = "岗位")
    @AutoMapping(target = "position")
    private String gwmc;

    /**
     * 学历
     */
    @TableField(value = "XL")
    @ApiModelProperty(value = "学历")
    @AutoMapping(target = "education")
    private String xl;

    /**
     * 婚姻状况
     */
    @TableField(value = "HYZK")
    @ApiModelProperty(value = "婚姻状况")
    @AutoMapping(target = "maritalStatus")
    private String hyzk;

    /**
     * 政治面貌
     */
    @TableField(value = "ZZMM")
    @ApiModelProperty(value = "政治面貌")
    @AutoMapping(target = "politicalStatus")
    private String zzmm;

    /**
     * 入党时间
     */
    @TableField(value = "RDSJ")
    @ApiModelProperty(value = "入党时间")
    @AutoMapping(target = "partyJoinDate", qualifiedByName = "strToLocalDate")
    private String rdsj;

    /**
     * 首次辅助时间
     */
    @TableField(value = "SCSJ")
    @ApiModelProperty(value = "首次辅助时间")
    @AutoMapping(target = "firstAuxiliaryDate", qualifiedByName = "strToLocalDate")
    private String scsj;

    /**
     * 首次工作时间
     */
    @TableField(value = "SCGZSJ")
    @ApiModelProperty(value = "首次工作时间")
    @AutoMapping(target = "firstWorkDate", qualifiedByName = "strToLocalDate")
    private String scgzsj;

    /**
     * 籍贯
     */
    @TableField(value = "JGMC")
    @ApiModelProperty(value = "籍贯")
    @AutoMapping(target = "nativePlace")
    private String jgmc;

    /**
     * 户籍地址
     */
    @TableField(value = "HJDZ")
    @ApiModelProperty(value = "户籍地址")
    @AutoMapping(target = "householdAddress")
    private String hjdz;

    /**
     * 工号
     */
    @TableField(value = "GH")
    @ApiModelProperty(value = "工号")
    @AutoMapping(target = "employeeNumber")
    private String gh;

    /**
     * 居住地
     */
    @TableField(value = "JZD")
    @ApiModelProperty(value = "居住地")
    @AutoMapping(target = "residenceAddress")
    private String jzd;

    /**
     * 劳务公司
     */
    @TableField(value = "LWGS")
    @ApiModelProperty(value = "劳务公司")
    @AutoMapping(target = "laborCompany")
    private String lwgs;

    /**
     * 保障渠道
     */
    @TableField(value = "BZQDMC")
    @ApiModelProperty(value = "保障渠道")
    @AutoMapping(target = "securityChannel")
    private String bzqdmc;

    /**
     * 手机号码
     */
    @TableField(value = "SJHM")
    @ApiModelProperty(value = "手机号码")
    @AutoMapping(target = "phoneNumber")
    private String sjhm;

    /**
     * 技术等级(职称)
     */
    @TableField(value = "ZC")
    @ApiModelProperty(value = "技术等级(职称)")
    @AutoMapping(target = "professionalTitle")
    private String zc;

    /**
     * 座机_内线
     */
    @TableField(value = "ZJ_NX")
    @ApiModelProperty(value = "座机_内线")
    @AutoMapping(target = "officePhoneInner")
    private String zjNx;

    /**
     * 座机_外线
     */
    @TableField(value = "ZJ_WX")
    @ApiModelProperty(value = "座机_外线")
    @AutoMapping(target = "officePhoneOuter")
    private String zjWx;

    /**
     * 血型
     */
    @TableField(value = "XXMC")
    @ApiModelProperty(value = "血型")
    @AutoMapping(target = "bloodType")
    private String xxmc;

    /**
     * 辅助工龄
     */
    @TableField(value = "FZGL")
    @ApiModelProperty(value = "辅助工龄")
    @AutoMapping(target = "auxiliaryServiceYears", qualifiedByName = "bigDecimalToString")
    private BigDecimal fzgl;

    /**
     * 责任领导警号
     */
    @TableField(value = "ZRLDJH")
    @ApiModelProperty(value = "责任领导警号")
    private String zrldjh;

    /**
     * 责任领导姓名
     */
    @TableField(value = "ZRLDXM")
    @ApiModelProperty(value = "责任领导姓名")
    private String zrldxm;

    /**
     * 带辅民警警号
     */
    @TableField(value = "DFMJJH")
    @ApiModelProperty(value = "带辅民警警号")
    private String dfmjjh;

    /**
     * 带辅民警姓名
     */
    @TableField(value = "DFMJXM")
    @ApiModelProperty(value = "带辅民警姓名")
    private String dfmjxm;

    /**
     * 健康状况
     */
    @TableField(value = "JKZKMC")
    @ApiModelProperty(value = "健康状况")
    private String jkzkmc;

    /**
     * 重大病史
     */
    @TableField(value = "ZDBS")
    @ApiModelProperty(value = "重大病史")
    private String zdbs;

    /**
     * 是否服兵役
     */
    @TableField(value = "SFBY")
    @ApiModelProperty(value = "是否服兵役")
    private String sfby;

    /**
     * 驾照
     */
    @TableField(value = "JZ")
    @ApiModelProperty(value = "驾照")
    private String jz;

    /**
     * 专业特长
     */
    @TableField(value = "ZYTC")
    @ApiModelProperty(value = "专业特长")
    private String zytc;

    /**
     * 民族
     */
    @TableField(value = "MZ")
    @ApiModelProperty(value = "民族")
    private String mz;
}