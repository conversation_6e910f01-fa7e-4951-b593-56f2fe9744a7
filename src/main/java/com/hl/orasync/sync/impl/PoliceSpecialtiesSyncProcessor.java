package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.entity.PoliceSpecialties;
import com.hl.archive.service.PoliceSpecialtiesService;
import com.hl.orasync.domain.VWjRytc;
import com.hl.orasync.service.VWjRytcService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceSpecialtiesSyncProcessor extends AbstractDataSyncProcessor<VWjRytc, PoliceSpecialties> {

    private final PoliceSpecialtiesService policeSpecialtiesService;

    private final VWjRytcService vwjRytcService;

    @Override
    protected IService<VWjRytc> getSourceService() {
        return vwjRytcService;
    }

    @Override
    protected IService<PoliceSpecialties> getTargetService() {
        return policeSpecialtiesService;
    }

    @Override
    protected Class<PoliceSpecialties> getTargetClass() {
        return PoliceSpecialties.class;
    }

    @Override
    protected LambdaQueryWrapper<VWjRytc> buildSourceQueryWrapper() {
        return Wrappers.<VWjRytc>lambdaQuery()
                .orderByDesc(VWjRytc::getGmsfhm);
    }

    @Override
    protected LambdaQueryWrapper<PoliceSpecialties> buildTargetQueryWrapper() {
        return Wrappers.<PoliceSpecialties>lambdaQuery()
                .eq(PoliceSpecialties::getDataType, 0);
    }

    @Override
    public Function<PoliceSpecialties, String> getBusinessKeyGenerator() {
        return policeSpecialties ->
                policeSpecialties.getIdCard() + "_" + policeSpecialties.getSpecialtyName() + "_"
                        + policeSpecialties.getAwardDate() + "_" + policeSpecialties.getApproveAuthority();
    }
}
