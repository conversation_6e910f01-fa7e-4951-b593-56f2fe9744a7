package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.entity.PoliceOnlineExam;
import com.hl.archive.service.PoliceOnlineExamService;
import com.hl.orasync.domain.VWjWsks;
import com.hl.orasync.service.VWjWsksService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceOnlineExamSyncProcessor extends AbstractDataSyncProcessor<VWjWsks, PoliceOnlineExam> {

    private final VWjWsksService vWjWsksService;

    private final PoliceOnlineExamService policeOnlineExamService;

    @Override
    protected IService<VWjWsks> getSourceService() {
        return vWjWsksService;
    }

    @Override
    protected IService<PoliceOnlineExam> getTargetService() {
        return policeOnlineExamService;
    }

    @Override
    protected Class<PoliceOnlineExam> getTargetClass() {
        return PoliceOnlineExam.class;
    }

    @Override
    protected LambdaQueryWrapper<VWjWsks> buildSourceQueryWrapper() {
        return Wrappers.<VWjWsks>lambdaQuery()
                .orderByDesc(VWjWsks::getGmsfhm)
                .orderByDesc(VWjWsks::getKscs);
    }

    @Override
    public Function<PoliceOnlineExam, String> getBusinessKeyGenerator() {
        return policeOnlineExam ->
                policeOnlineExam.getIdCard() + "_" +
                        policeOnlineExam.getExamPaperName() + "_" +
                        policeOnlineExam.getStartTime() + "_" +
                        policeOnlineExam.getSubmitStatus() + "_" +
                        policeOnlineExam.getEndTime();
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjWsksService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeOnlineExamService.count();
    }
}
