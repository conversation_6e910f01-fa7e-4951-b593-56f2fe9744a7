package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.service.AuxiliaryPoliceInfoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjbxxFjxx;
import com.hl.orasync.service.VWjRyjbxxFjxxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class AuxiliaryPoliceInfoSyncProcessor implements DataSyncProcessor<VWjRyjbxxFjxx, AuxiliaryPoliceInfo> {

    private final VWjRyjbxxFjxxService vWjRyjbxxFjxxService;

    private final AuxiliaryPoliceInfoService auxiliaryPoliceInfoService;

    private final Converter converter;

    @Override
    public List<VWjRyjbxxFjxx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyjbxxFjxx> page = vWjRyjbxxFjxxService.page(new Page<>(offset, limit), Wrappers.<VWjRyjbxxFjxx>lambdaQuery()
                .orderByDesc(VWjRyjbxxFjxx::getPersonid));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<AuxiliaryPoliceInfo> getTargetData(int offset, int limit) {
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public AuxiliaryPoliceInfo convert(VWjRyjbxxFjxx source) {
        return converter.convert(source, AuxiliaryPoliceInfo.class);
    }

    @Override
    public Function<AuxiliaryPoliceInfo, String> getBusinessKeyGenerator() {
        return AuxiliaryPoliceInfo::getIdCard;
    }

    @Override
    public void batchInsert(List<AuxiliaryPoliceInfo> records) {
        auxiliaryPoliceInfoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<AuxiliaryPoliceInfo> records) {
        auxiliaryPoliceInfoService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        auxiliaryPoliceInfoService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vWjRyjbxxFjxxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return auxiliaryPoliceInfoService.count();
    }
}
