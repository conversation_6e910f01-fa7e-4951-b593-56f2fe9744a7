package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.service.AuxiliaryPoliceInfoService;
import com.hl.orasync.domain.VWjRyjbxxFjxx;
import com.hl.orasync.service.VWjRyjbxxFjxxService;
import com.hl.orasync.sync.AbstractDataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class AuxiliaryPoliceInfoSyncProcessor extends AbstractDataSyncProcessor<VWjRyjbxxFjxx, AuxiliaryPoliceInfo> {

    private final VWjRyjbxxFjxxService vWjRyjbxxFjxxService;

    private final AuxiliaryPoliceInfoService auxiliaryPoliceInfoService;

    @Override
    protected IService<VWjRyjbxxFjxx> getSourceService() {
        return vWjRyjbxxFjxxService;
    }

    @Override
    protected IService<AuxiliaryPoliceInfo> getTargetService() {
        return auxiliaryPoliceInfoService;
    }

    @Override
    protected Class<AuxiliaryPoliceInfo> getTargetClass() {
        return AuxiliaryPoliceInfo.class;
    }

    @Override
    protected LambdaQueryWrapper<VWjRyjbxxFjxx> buildSourceQueryWrapper() {
        return Wrappers.<VWjRyjbxxFjxx>lambdaQuery()
                .orderByDesc(VWjRyjbxxFjxx::getPersonid);
    }

    @Override
    public Function<AuxiliaryPoliceInfo, String> getBusinessKeyGenerator() {
        return AuxiliaryPoliceInfo::getIdCard;
    }
}
