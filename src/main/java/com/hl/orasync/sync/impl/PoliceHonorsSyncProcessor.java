package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.archive.service.PoliceHonorsService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjBzjlGrry;
import com.hl.orasync.domain.VWjRybzjl;
import com.hl.orasync.domain.VWjRyjdkh;
import com.hl.orasync.service.VWjBzjlGrryService;
import com.hl.orasync.service.VWjRybzjlService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceHonorsSyncProcessor implements DataSyncProcessor<VWjBzjlGrry, PoliceHonors> {

    private final Converter converter;

    private final PoliceHonorsService policeHonorsService;

    private final VWjBzjlGrryService vwjBzjlGrryService;


    @Override
    public List<VWjBzjlGrry> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));

        Page<VWjBzjlGrry> page = vwjBzjlGrryService.page(Page.of(offset, limit),
                Wrappers.<VWjBzjlGrry>lambdaQuery()
                        .orderByDesc(VWjBzjlGrry::getXxzjbh));

        DynamicDataSourceContextHolder.clearDataSourceType();

        return page.getRecords();
    }

    @Override
    public List<PoliceHonors> getTargetData(int offset, int limit) {
        Page<PoliceHonors> page = policeHonorsService.page(Page.of(offset, limit),Wrappers.<PoliceHonors>lambdaQuery()
                .eq(PoliceHonors::getSourceType,0));
        return page.getRecords();
    }

    @Override
    public PoliceHonors convert(VWjBzjlGrry source) {
        return converter.convert(source, PoliceHonors.class);
    }

    @Override
    public Function<PoliceHonors, String> getBusinessKeyGenerator() {
        return PoliceHonors::getBh;
    }

    @Override
    public void batchInsert(List<PoliceHonors> records) {
        policeHonorsService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceHonors> records) {

        policeHonorsService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeHonorsService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjBzjlGrryService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeHonorsService.count(Wrappers.<PoliceHonors>lambdaQuery()
                .eq(PoliceHonors::getSourceType,0));
    }
}
