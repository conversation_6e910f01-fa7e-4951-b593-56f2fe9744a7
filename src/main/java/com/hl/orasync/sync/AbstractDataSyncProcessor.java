package com.hl.orasync.sync;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.function.Function;

/**
 * 数据同步处理器抽象基类
 * 提取所有实现类中的通用逻辑，减少重复代码
 * 
 * @param <S> 源数据类型
 * @param <T> 目标数据类型
 */
@RequiredArgsConstructor
public abstract class AbstractDataSyncProcessor<S, T> implements DataSyncProcessor<S, T> {

    /**
     * 通用转换器，大部分实现类都使用这个
     */
    protected final Converter converter;

    /**
     * 获取源数据服务
     * 子类需要实现此方法返回对应的源数据服务
     */
    protected abstract IService<S> getSourceService();

    /**
     * 获取目标数据服务
     * 子类需要实现此方法返回对应的目标数据服务
     */
    protected abstract IService<T> getTargetService();

    /**
     * 获取源数据查询条件构建器
     * 子类可以重写此方法来自定义查询条件和排序
     * 默认按照 gmsfhm 字段降序排列
     */
    protected LambdaQueryWrapper<S> buildSourceQueryWrapper() {
        return Wrappers.<S>lambdaQuery();
    }

    /**
     * 获取目标数据查询条件构建器
     * 子类可以重写此方法来自定义查询条件
     * 默认无条件查询
     */
    protected LambdaQueryWrapper<T> buildTargetQueryWrapper() {
        return Wrappers.<T>lambdaQuery();
    }

    /**
     * 获取目标数据类型
     * 子类需要实现此方法返回目标数据的Class对象
     * 用于通用转换器的类型转换
     */
    protected abstract Class<T> getTargetClass();

    /**
     * 获取源数据 - 支持分页
     * 统一处理数据源切换逻辑
     */
    @Override
    public List<S> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        try {
            Page<S> page = getSourceService().page(
                Page.of(offset, limit), 
                buildSourceQueryWrapper()
            );
            return page.getRecords();
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 获取目标数据 - 支持分页
     * 统一处理分页查询逻辑
     */
    @Override
    public List<T> getTargetData(int offset, int limit) {
        Page<T> page = getTargetService().page(
            Page.of(offset, limit), 
            buildTargetQueryWrapper()
        );
        return page.getRecords();
    }

    /**
     * 数据转换
     * 默认使用通用转换器进行转换
     * 子类可以重写此方法实现自定义转换逻辑
     */
    @Override
    public T convert(S source) {
        return converter.convert(source, getTargetClass());
    }

    /**
     * 批量插入
     * 统一使用 MyBatis-Plus 的 saveBatch 方法
     */
    @Override
    public void batchInsert(List<T> records) {
        getTargetService().saveBatch(records);
    }

    /**
     * 批量更新
     * 统一使用 MyBatis-Plus 的 updateBatchById 方法
     */
    @Override
    public void batchUpdate(List<T> records) {
        getTargetService().updateBatchById(records);
    }

    /**
     * 批量删除
     * 统一使用 MyBatis-Plus 的 removeByIds 方法
     */
    @Override
    public void batchDelete(List<String> businessKeys) {
        getTargetService().removeByIds(businessKeys);
    }

    /**
     * 获取源数据总数
     * 统一处理数据源切换逻辑
     */
    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        try {
            return getSourceService().count(buildSourceQueryWrapper());
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 获取目标数据总数
     * 统一处理计数查询逻辑
     */
    @Override
    public long getTargetDataCount() {
        return getTargetService().count(buildTargetQueryWrapper());
    }
}
