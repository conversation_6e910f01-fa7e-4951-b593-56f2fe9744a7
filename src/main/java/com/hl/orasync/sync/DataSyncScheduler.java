package com.hl.orasync.sync;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.hl.orasync.sync.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DataSyncScheduler {

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private PoliceBasicInfoSyncProcessor policeBasicInfoSyncProcessor;

    @Autowired
    private PolicePositionRankSyncProcessor policePositionRankSyncProcessor;

    @Autowired
    private PoliceRankInfoSyncProcessor policeRankInfoSyncProcessor;

    @Autowired
    private PoliceContactInfoSyncProcessor policeContactInfoSyncProcessor;

    @Autowired
    private PolicePoliticalStatusSyncProcessor policePoliticalStatusSyncProcessor;

    @Autowired
    private PoliceEducationSyncProcessor policeEducationSyncProcessor;

    @Autowired
    private PoliceResumeSyncProcessor policeResumeSyncProcessor;

    @Autowired
    private PoliceTrainingSyncProcessor policeTrainingSyncProcessor;


    @Autowired
    private PoliceAnnualAssessmentSyncProcessor policeAnnualAssessmentSyncProcessor;


    @Autowired
    private PoliceQuarterlyAssessmentSyncProcessor policeQuarterlyAssessmentSyncProcessor;


    @Autowired
    private PoliceMonthlyAssessmentSyncProcessor policeMonthlyAssessmentSyncProcessor;

    @Autowired
    private PoliceFamilyMembersSyncProcessor policeFamilyMembersSyncProcessor;

    @Autowired
    private PoliceHonorsSyncProcessor policeHonorsSyncProcessor;

    @Autowired
    private PoliceSpecialtiesSyncProcessor policeSpecialtiesSyncProcessor;

    @Autowired
    private PoliceTrainingRecordsSyncProcessor policeTrainingRecordsSyncProcessor;

    @Autowired
    private PoliceOnlineExamSyncProcessor policeOnlineExamSyncProcessor;

    @Autowired
    private PoliceDishonestExecutorSyncProcessor policeDishonestExecutorSyncProcessor;

    @Autowired
    private PoliceOtherMattersSyncProcessor policeOtherMattersSyncProcessor;

    @Autowired
    private PoliceFamilySecuritiesInsuranceSyncProcessor policeFamilySecuritiesInsuranceSyncProcessor;

    @Autowired
    private PoliceFamilyVehiclesSyncProcessor policeFamilyVehiclesSyncProcessor;

    @Autowired
    private PoliceFamilyRealEstateSyncProcessor policeFamilyRealEstateSyncProcessor;

    @Autowired
    private PoliceFamilyPrivateEquityFundSyncProcessor policeFamilyPrivateEquityFundSyncProcessor;

    @Autowired
    private PoliceFamilyPaidInstitutionsSyncProcessor policeFamilyPaidInstitutionsSyncProcessor;

    @Autowired
    private PoliceFamilyBusinessSyncProcessor policeFamilyBusinessSyncProcessor;

    @Autowired
    private PoliceFamilyCriminalLiabilitySyncProcessor policeFamilyCriminalLiabilitySyncProcessor;

    @Autowired
    private PoliceFamilyOverseasMigrationSyncProcessor policeFamilyOverseasMigrationSyncProcessor;

    @Autowired
    private PoliceChildrenForeignMarriageSyncProcessor policeChildrenForeignMarriageSyncProcessor;

    @Autowired
    private PoliceOrganizationalInquirySyncProcessor policeOrganizationalInquirySyncProcessor;

    @Autowired
    private PoliceHealthStatusSyncProcessor policeHealthStatusSyncProcessor;

    @Autowired
    private PoliceInvestmentInfoSyncProcessor policeInvestmentInfoSyncProcessor;

    @Autowired
    private PoliceWeddingFuneralEventsSyncProcessor policeWeddingFuneralEventsSyncProcessor;

    @Autowired
    private PoliceLoanInfoSyncProcessor policeLoanInfoSyncProcessor;

    @Autowired
    private PoliceHkMacauTaiwanTravelSyncProcessor policeHkMacauTaiwanTravelSyncProcessor;

    @Autowired
    private PoliceHkMacauTaiwanPermitSyncProcessor policeHkMacauTaiwanPermitSyncProcessor;

    @Autowired
    private PoliceOverseasTravelSyncProcessor policeOverseasTravelSyncProcessor;

    @Autowired
    private PolicePassportSyncProcessor policePassportSyncProcessor;

    @Autowired
    private PoliceMarriageStatusSyncProcessor policeMarriageStatusSyncProcessor;

    @Autowired
    private PoliceProjectEntryPersonSyncProcessor policeProjectEntryPersonSyncProcessor;

    @Autowired
    private PoliceProjectContactSyncProcessor policeProjectContactSyncProcessor;

    @Autowired
    private PoliceProjectMaterialSyncProcessor policeProjectMaterialSyncProcessor;

    @Autowired
    private PoliceProjectStorySyncProcessor policeProjectStorySyncProcessor;

    @Autowired
    private PoliceUnitAwardSyncProcessor policeUnitAwardSyncProcessor;

    @Autowired
    private PoliceMomentSubmissionSyncProcessor policeMomentSubmissionSyncProcessor;

    @Autowired
    private PoliceMomentSubmissionVideoSyncProcessor policeMomentSubmissionVideoSyncProcessor;

    @Autowired
    private PoliceCapabilityEvalSyncProcessor policeCapabilityEvalSyncProcessor;

    @Autowired
    private PoliceInjuryDeclareSyncProcessor policeInjuryDeclareSyncProcessor;

    @Autowired
    private PoliceViolationSummarySyncProcessor policeViolationSummarySyncProcessor;

    @Autowired
    private PoliceViolationPersonSyncProcessor policeViolationPersonSyncProcessor;

    @Autowired
    private PoliceViolationResultSyncProcessor policeViolationResultSyncProcessor;

    @Autowired
    private AuxiliaryPoliceInfoSyncProcessor auxiliaryPoliceInfoSyncProcessor;

    @JobExecutor(name = "syncPoliceBasicInfo")
    public void syncPoliceBasicInfo() {
        SnailJobLog.REMOTE.info("开始执行民警基本数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeBasicInfoSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警基本数据数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            SnailJobLog.REMOTE.error("民警基本数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPolicePositionRank")
    public void syncPolicePositionRank() {
        SnailJobLog.REMOTE.info("开始执行民警职务数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policePositionRankSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警职务数据数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警职务数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceRank")
    public void syncPoliceRank() {
        SnailJobLog.REMOTE.info("开始执行民警警衔数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeRankInfoSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警警衔数据数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警警衔数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceContactInfo")
    public void syncPoliceContactInfo() {
        SnailJobLog.REMOTE.info("开始执行民警家庭地址数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeContactInfoSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭地址数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭地址数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPolicePoliticalStatus")
    public void syncPolicePoliticalStatus() {
        SnailJobLog.REMOTE.info("开始执行民警政治面貌数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policePoliticalStatusSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警政治面貌数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警政治面貌数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceEducation")
    public void syncPoliceEducation() {
        SnailJobLog.REMOTE.info("开始执行民警学历学位数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeEducationSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警学历学位数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警学历学位数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceResume")
    public void syncPoliceResume() {
        SnailJobLog.REMOTE.info("开始执行民警个人简历数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeResumeSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警个人简历数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警个人简历数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceTraining")
    public void syncPoliceTraining() {
        SnailJobLog.REMOTE.info("开始执行民警教育训练数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeTrainingSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警教育训练数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警教育训练数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceAnnualAssessment")
    public void syncPoliceAnnualAssessment() {
        SnailJobLog.REMOTE.info("开始执行民警年度考核数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeAnnualAssessmentSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警年度考核数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警年度考核数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceQuarterlyAssessment")
    public void syncPoliceQuarterlyAssessment() {
        SnailJobLog.REMOTE.info("开始执行民警季度考核数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeQuarterlyAssessmentSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警季度考核数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警季度考核数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceMonthlyAssessment")
    public void syncPoliceMonthlyAssessment() {
        SnailJobLog.REMOTE.info("开始执行民警月度考核数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeMonthlyAssessmentSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警月度考核数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警月度考核数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceFamilyMembers")
    public void syncPoliceFamilyMembers() {
        SnailJobLog.REMOTE.info("开始执行民警家庭成员同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyMembersSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警民警家庭成员同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警民警家庭成员同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceHonors")
    public void syncPoliceHonors() {
        SnailJobLog.REMOTE.info("开始执行民警表彰奖励同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeHonorsSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警民警表彰奖励同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警民警表彰奖励同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceSpecialties")
    public void syncPoliceSpecialties() {
        SnailJobLog.REMOTE.info("开始执行民警特长同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeSpecialtiesSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警民警特长同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警民警特长同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceTrainingRecords")
    public void syncPoliceTrainingRecords() {
        SnailJobLog.REMOTE.info("开始执行民警培训记录数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(20000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeTrainingRecordsSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警培训记录数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警培训记录数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceOnlineExam")
    public void syncPoliceOnlineExam() {
        SnailJobLog.REMOTE.info("开始执行民警在线考试数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(100000)
                .enableParallelProcessing(false)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeOnlineExamSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警在线考试数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警在线考试数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceDishonestExecutor")
    public void syncPoliceDishonestExecutor() {
        SnailJobLog.REMOTE.info("开始执行民警失信被执行人数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeDishonestExecutorSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警失信被执行人数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警失信被执行人数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceOtherMatters")
    public void syncPoliceOtherMatters() {
        SnailJobLog.REMOTE.info("开始执行民警其他事项数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeOtherMattersSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警其他事项数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警其他事项数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilySecuritiesInsurance")
    public void syncPoliceFamilySecuritiesInsurance() {
        SnailJobLog.REMOTE.info("开始执行民警家庭证券保险数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilySecuritiesInsuranceSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭证券保险数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭证券保险数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyVehicles")
    public void syncPoliceFamilyVehicles() {
        SnailJobLog.REMOTE.info("开始执行民警家庭车辆数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyVehiclesSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭车辆数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭车辆数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyRealEstate")
    public void syncPoliceFamilyRealEstate() {
        SnailJobLog.REMOTE.info("开始执行民警家庭房产数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyRealEstateSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭房产数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭房产数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyPrivateEquityFund")
    public void syncPoliceFamilyPrivateEquityFund() {
        SnailJobLog.REMOTE.info("开始执行民警家庭私募基金数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyPrivateEquityFundSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭私募基金数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭私募基金数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyPaidInstitutions")
    public void syncPoliceFamilyPaidInstitutions() {
        SnailJobLog.REMOTE.info("开始执行民警家庭付费机构数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyPaidInstitutionsSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭付费机构数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭付费机构数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyBusiness")
    public void syncPoliceFamilyBusiness() {
        SnailJobLog.REMOTE.info("开始执行民警家庭经商办企业数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyBusinessSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭经商办企业数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭经商办企业数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyCriminalLiability")
    public void syncPoliceFamilyCriminalLiability() {
        SnailJobLog.REMOTE.info("开始执行民警家庭刑事责任数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyCriminalLiabilitySyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭刑事责任数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭刑事责任数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceFamilyOverseasMigration")
    public void syncPoliceFamilyOverseasMigration() {
        SnailJobLog.REMOTE.info("开始执行民警家庭境外移居数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeFamilyOverseasMigrationSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警家庭境外移居数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警家庭境外移居数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceChildrenForeignMarriage")
    public void syncPoliceChildrenForeignMarriage() {
        SnailJobLog.REMOTE.info("开始执行民警子女涉外婚姻数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeChildrenForeignMarriageSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警子女涉外婚姻数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警子女涉外婚姻数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceOrganizationalInquiry")
    public void syncPoliceOrganizationalInquiry() {
        SnailJobLog.REMOTE.info("开始执行民警组织查询数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeOrganizationalInquirySyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警组织查询数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警组织查询数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceHealthStatus")
    public void syncPoliceHealthStatus() {
        SnailJobLog.REMOTE.info("开始执行民警健康状况数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeHealthStatusSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警健康状况数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警健康状况数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceInvestmentInfo")
    public void syncPoliceInvestmentInfo() {
        SnailJobLog.REMOTE.info("开始执行民警投资信息数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeInvestmentInfoSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警投资信息数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警投资信息数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceWeddingFuneralEvents")
    public void syncPoliceWeddingFuneralEvents() {
        SnailJobLog.REMOTE.info("开始执行民警婚丧喜庆数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeWeddingFuneralEventsSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警婚丧喜庆数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警婚丧喜庆数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceLoanInfo")
    public void syncPoliceLoanInfo() {
        SnailJobLog.REMOTE.info("开始执行民警借贷信息数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeLoanInfoSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警借贷信息数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警借贷信息数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceHkMacauTaiwanTravel")
    public void syncPoliceHkMacauTaiwanTravel() {
        SnailJobLog.REMOTE.info("开始执行民警港澳台旅行数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeHkMacauTaiwanTravelSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警港澳台旅行数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警港澳台旅行数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceHkMacauTaiwanPermit")
    public void syncPoliceHkMacauTaiwanPermit() {
        SnailJobLog.REMOTE.info("开始执行民警港澳台通行证数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeHkMacauTaiwanPermitSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警港澳台通行证数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警港澳台通行证数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceOverseasTravel")
    public void syncPoliceOverseasTravel() {
        SnailJobLog.REMOTE.info("开始执行民警境外旅行数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeOverseasTravelSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警境外旅行数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警境外旅行数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPolicePassport")
    public void syncPolicePassport() {
        SnailJobLog.REMOTE.info("开始执行民警护照数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policePassportSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警护照数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警护照数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceMarriageStatus")
    public void syncPoliceMarriageStatus() {
        SnailJobLog.REMOTE.info("开始执行民警婚姻状况数据同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeMarriageStatusSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警婚姻状况数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警婚姻状况数据同步失败: {}", result.getErrorMessage());
        }
    }



    @JobExecutor(name = "syncPoliceProjectEntryPerson")
    public void syncPoliceProjectEntryPerson(){
        SnailJobLog.REMOTE.info("开始执行同步星火计划个人同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeProjectEntryPersonSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("星火计划-入项个人数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("星火计划-入项个人数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceProjectContact")
    public void syncPoliceProjectContact(){
        SnailJobLog.REMOTE.info("开始执行同步星火计划个人详情-培养联系同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeProjectContactSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("星火计划个人详情-培养联系数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("星火计划个人详情-培养联系数据同步失败: {}", result.getErrorMessage());
        }
    }
    @JobExecutor(name = "syncPoliceProjectMaterial")
    public void syncPoliceProjectMaterial(){
        SnailJobLog.REMOTE.info("开始执行同步星火计划个人详情-图文资料同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeProjectMaterialSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("星火计划个人详情-图文资料数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("星火计划个人详情-图文资料数据同步失败: {}", result.getErrorMessage());
        }
    }
    @JobExecutor(name = "syncPoliceProjectStory")
    public void syncPoliceProjectStory(){
        SnailJobLog.REMOTE.info("开始执行星火计划个人详情-先进事迹同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeProjectStorySyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("星火计划个人详情-先进事迹数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("星火计划个人详情-先进事迹数据同步失败: {}", result.getErrorMessage());
        }
    }
    @JobExecutor(name = "syncPoliceUnitAward")
    public void syncPoliceUnitAward(){
        SnailJobLog.REMOTE.info("开始执行同步表彰奖励-单位荣誉同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeUnitAwardSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("表彰奖励-单位荣誉数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("表彰奖励-单位荣誉数据同步失败: {}", result.getErrorMessage());
        }
    }
    @JobExecutor(name = "syncPoliceMomentSubmission")
    public void syncPoliceMomentSubmission(){
        SnailJobLog.REMOTE.info("开始执行同步警彩瞬间-素材上报同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeMomentSubmissionSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("警彩瞬间-素材上报数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("警彩瞬间-素材上报数据同步失败: {}", result.getErrorMessage());
        }
    }


    @JobExecutor(name = "syncPoliceMomentSubmissionVideo")
    public void syncPoliceMomentSubmissionVideo(){
        SnailJobLog.REMOTE.info("开始执行同步警彩瞬间-视频同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeMomentSubmissionVideoSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("警彩瞬间-视频数据同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("警彩瞬间-视频数据同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceCapabilityEval")
    public void syncPoliceCapabilityEval(){
        SnailJobLog.REMOTE.info("开始执行能力参评任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeCapabilityEvalSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("能力参评同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("能力参评同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceInjuryDeclare")
    public void syncPoliceInjuryDeclare(){
        SnailJobLog.REMOTE.info("开始执行民警受伤信息申报同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeInjuryDeclareSyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("民警受伤信息申报同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("民警受伤信息申报同步失败: {}", result.getErrorMessage());
        }
    }

    @JobExecutor(name = "syncPoliceViolationSummary")
    public void syncPoliceViolationSummary(){
        SnailJobLog.REMOTE.info("开始执行违规违纪问题汇总同步任务");
        SyncConfig config = SyncConfig.builder()
                .pageSize(5000)
                .enableParallelProcessing(true)
                .threadPoolSize(4)
                .build();

        SyncResult result = dataSyncService.syncData(policeViolationSummarySyncProcessor, config);

        if (result.isSuccess()) {
            SnailJobLog.REMOTE.info("违规违纪问题汇总同步成功: 新增={}, 更新={}, 删除={}",
                    result.getInsertCount(), result.getUpdateCount(), result.getDeleteCount());
        } else {
            log.error("违规违纪问题汇总同步失败: {}", result.getErrorMessage());
        }
    }


}
