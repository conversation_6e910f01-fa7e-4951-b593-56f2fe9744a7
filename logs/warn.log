2025-07-16 21:47:45.351 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-07-16 21:47:45.362 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-07-16 21:47:45.372 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-07-16 21:47:46.297 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:47:46.297 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:47:48.751 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-07-16 21:47:48.751 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.756 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-07-16 21:47:48.756 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.760 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-07-16 21:47:48.760 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.765 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-07-16 21:47:48.765 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.769 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-07-16 21:47:48.769 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.774 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-07-16 21:47:48.774 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.778 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-07-16 21:47:48.778 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.782 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-07-16 21:47:48.782 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.786 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-07-16 21:47:48.787 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.790 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-07-16 21:47:48.790 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.795 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-07-16 21:47:48.795 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.799 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-07-16 21:47:48.799 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.803 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-07-16 21:47:48.803 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.807 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-07-16 21:47:48.807 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.812 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-07-16 21:47:48.812 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.817 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-07-16 21:47:48.818 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.822 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-07-16 21:47:48.822 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.827 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-07-16 21:47:48.827 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.832 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-07-16 21:47:48.832 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.839 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-07-16 21:47:48.839 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.845 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-07-16 21:47:48.845 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.851 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-07-16 21:47:48.851 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.857 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-07-16 21:47:48.857 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.863 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-07-16 21:47:48.863 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.869 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-07-16 21:47:48.869 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.875 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-07-16 21:47:48.875 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.880 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-07-16 21:47:48.880 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.886 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-07-16 21:47:48.886 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.891 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-07-16 21:47:48.891 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.897 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-07-16 21:47:48.897 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.903 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-07-16 21:47:48.903 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.908 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-07-16 21:47:48.908 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.914 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-07-16 21:47:48.914 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.920 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-07-16 21:47:48.920 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.925 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-07-16 21:47:48.926 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.931 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-07-16 21:47:48.931 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.936 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-07-16 21:47:48.936 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.942 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-07-16 21:47:48.942 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.947 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-07-16 21:47:48.947 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.954 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-07-16 21:47:48.954 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.962 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-07-16 21:47:48.962 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.969 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-07-16 21:47:48.970 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.975 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-07-16 21:47:48.975 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.980 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-07-16 21:47:48.980 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:48:02.800 WARN  [RMI TCP Connection(6)-192.168.123.182] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_452]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_452]
	at sun.reflect.GeneratedMethodAccessor67.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-07-16 21:53:11.202 WARN  [Thread-10] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-16 21:53:11.202 WARN  [Thread-5] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-16 21:53:11.203 WARN  [Thread-10] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-16 21:53:11.203 WARN  [Thread-5] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-16 21:56:27.763 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-07-16 21:56:27.774 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-07-16 21:56:27.784 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-07-16 21:56:28.654 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:56:28.654 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:56:31.091 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-07-16 21:56:31.092 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.096 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-07-16 21:56:31.096 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.100 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-07-16 21:56:31.100 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.104 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-07-16 21:56:31.104 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.108 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-07-16 21:56:31.108 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.112 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-07-16 21:56:31.112 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.116 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-07-16 21:56:31.116 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.120 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-07-16 21:56:31.120 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.124 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-07-16 21:56:31.124 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.128 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-07-16 21:56:31.128 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.133 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-07-16 21:56:31.133 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.138 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-07-16 21:56:31.138 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.143 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-07-16 21:56:31.143 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.147 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-07-16 21:56:31.147 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.152 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-07-16 21:56:31.152 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.158 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-07-16 21:56:31.158 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.162 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-07-16 21:56:31.163 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.172 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-07-16 21:56:31.172 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.176 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-07-16 21:56:31.176 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.181 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-07-16 21:56:31.181 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.185 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-07-16 21:56:31.185 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.188 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-07-16 21:56:31.188 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.192 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-07-16 21:56:31.192 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.196 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-07-16 21:56:31.196 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.200 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-07-16 21:56:31.200 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.203 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-07-16 21:56:31.204 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.207 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-07-16 21:56:31.207 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.211 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-07-16 21:56:31.211 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.214 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-07-16 21:56:31.214 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.218 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-07-16 21:56:31.218 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.222 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-07-16 21:56:31.222 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.225 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-07-16 21:56:31.225 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.229 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-07-16 21:56:31.229 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.234 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-07-16 21:56:31.234 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.238 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-07-16 21:56:31.238 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.242 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-07-16 21:56:31.242 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.246 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-07-16 21:56:31.246 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.250 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-07-16 21:56:31.250 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.254 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-07-16 21:56:31.254 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.260 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-07-16 21:56:31.260 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.267 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-07-16 21:56:31.267 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.273 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-07-16 21:56:31.273 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.277 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-07-16 21:56:31.278 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.282 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-07-16 21:56:31.282 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:44.911 WARN  [RMI TCP Connection(3)-192.168.123.182] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_452]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_452]
	at sun.reflect.GeneratedMethodAccessor55.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-07-16 21:56:48.180 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-16 21:56:48.180 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-16 21:56:48.180 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-16 21:56:48.181 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-01 23:33:54.777 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-01 23:33:54.814 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-01 23:33:54.848 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewArchiveCaseTaskInfoDocumentMapper' and 'com.hl.archive.search.mapper.ViewArchiveCaseTaskInfoDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:58.712 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-01 23:33:58.712 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.717 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-01 23:33:58.717 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.722 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-01 23:33:58.722 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.727 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-01 23:33:58.727 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.732 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-01 23:33:58.732 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.737 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-01 23:33:58.737 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.742 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-01 23:33:58.742 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.747 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-01 23:33:58.748 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.753 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-01 23:33:58.753 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.765 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-01 23:33:58.765 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.770 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-01 23:33:58.770 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.775 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-01 23:33:58.775 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.780 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-01 23:33:58.780 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.784 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-01 23:33:58.785 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.790 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-01 23:33:58.790 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.794 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-01 23:33:58.794 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.798 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-01 23:33:58.799 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.802 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-01 23:33:58.802 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.806 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-01 23:33:58.806 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.811 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-01 23:33:58.812 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.816 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-01 23:33:58.816 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.821 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-01 23:33:58.821 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.825 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-01 23:33:58.825 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.829 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-01 23:33:58.830 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.834 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-01 23:33:58.834 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.838 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-01 23:33:58.838 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.842 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-01 23:33:58.842 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.846 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-01 23:33:58.846 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.850 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-01 23:33:58.850 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.854 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-01 23:33:58.854 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.858 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-01 23:33:58.858 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.862 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-01 23:33:58.862 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.867 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-01 23:33:58.867 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.872 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-01 23:33:58.872 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.877 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-01 23:33:58.877 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.881 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-01 23:33:58.881 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.885 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-01 23:33:58.885 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.889 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-01 23:33:58.889 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.894 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-01 23:33:58.894 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.900 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-01 23:33:58.900 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.907 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-01 23:33:58.907 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.914 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-01 23:33:58.914 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.919 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-01 23:33:58.919 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.925 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-01 23:33:58.925 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:34:35.729 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 23:34:35.729 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-01 23:34:35.729 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-01 23:34:35.729 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
