2025-07-16 21:47:43.933 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-16 21:47:43.946 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-16 21:47:44.332 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:47:44.332 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:47:45.351 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-07-16 21:47:45.362 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-07-16 21:47:45.372 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-07-16 21:47:45.373 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-07-16 21:47:45.392 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-07-16 21:47:46.102 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-16 21:47:46.105 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-16 21:47:46.123 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Elasticsearch repository interfaces.
2025-07-16 21:47:46.126 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-16 21:47:46.126 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-16 21:47:46.133 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-16 21:47:46.140 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-16 21:47:46.141 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-16 21:47:46.151 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-16 21:47:46.297 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:47:46.297 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:47:46.483 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=7ffefe6d-bc56-315f-af6d-6c06d983e88e
2025-07-16 21:47:46.846 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:47:46.849 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:47:46.850 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$510/210204797] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:47:46.856 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:47:46.937 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$b733392d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:47:46.939 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$14bf9732] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:47:47.312 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-07-16 21:47:47.321 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-07-16 21:47:47.322 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-07-16 21:47:47.322 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-16 21:47:47.410 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 21:47:47.410 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2007 ms
2025-07-16 21:47:47.555 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-07-16 21:47:47.846 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-07-16 21:47:48.751 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-07-16 21:47:48.751 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.756 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-07-16 21:47:48.756 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.760 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-07-16 21:47:48.760 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.765 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-07-16 21:47:48.765 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.769 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-07-16 21:47:48.769 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.774 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-07-16 21:47:48.774 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.778 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-07-16 21:47:48.778 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.782 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-07-16 21:47:48.782 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.786 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-07-16 21:47:48.787 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.790 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-07-16 21:47:48.790 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.795 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-07-16 21:47:48.795 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.799 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-07-16 21:47:48.799 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.803 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-07-16 21:47:48.803 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.807 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-07-16 21:47:48.807 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.812 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-07-16 21:47:48.812 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.817 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-07-16 21:47:48.818 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.822 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-07-16 21:47:48.822 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.827 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-07-16 21:47:48.827 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.832 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-07-16 21:47:48.832 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.839 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-07-16 21:47:48.839 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.845 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-07-16 21:47:48.845 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.851 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-07-16 21:47:48.851 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.857 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-07-16 21:47:48.857 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.863 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-07-16 21:47:48.863 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.869 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-07-16 21:47:48.869 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.875 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-07-16 21:47:48.875 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.880 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-07-16 21:47:48.880 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.886 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-07-16 21:47:48.886 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.891 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-07-16 21:47:48.891 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.897 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-07-16 21:47:48.897 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.903 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-07-16 21:47:48.903 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.908 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-07-16 21:47:48.908 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.914 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-07-16 21:47:48.914 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.920 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-07-16 21:47:48.920 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.925 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-07-16 21:47:48.926 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.931 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-07-16 21:47:48.931 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.936 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-07-16 21:47:48.936 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.942 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-07-16 21:47:48.942 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.947 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-07-16 21:47:48.947 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.954 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-07-16 21:47:48.954 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.962 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-07-16 21:47:48.962 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.969 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-07-16 21:47:48.970 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.975 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-07-16 21:47:48.975 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:48.980 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-07-16 21:47:48.980 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:47:50.756 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-07-16 21:47:51.091 INFO  [main] easy-es - ===> manual index mode activated
2025-07-16 21:47:51.125 INFO  [main] easy-es - ===> manual index mode activated
2025-07-16 21:47:53.603 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:47:53.603 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:47:53.954 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"192.168.10.104:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-07-16 21:47:55.849 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-16 21:47:56.140 INFO  [main] c.h.s.c.SecurityConfig - [/dict-test/test, /error/logs, /dict/api-query, /test/dict, /dict/add-batch] --> 200
2025-07-16 21:47:56.205 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4756c8f3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3cc9bde9, org.springframework.security.web.context.SecurityContextPersistenceFilter@48a2fb11, org.springframework.security.web.header.HeaderWriterFilter@2408ca4c, org.springframework.security.web.authentication.logout.LogoutFilter@2c914aa3, org.springframework.web.filter.CorsFilter@75bbeb89, com.hl.security.config.sso.SsoAuthTokenFilter@36d39655, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@56554e7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@b7e53b1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@197bf5ef, org.springframework.security.web.session.SessionManagementFilter@17abef0f, org.springframework.security.web.access.ExceptionTranslationFilter@38007ead, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@37bafef8]
2025-07-16 21:47:57.815 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-07-16 21:47:57.833 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-07-16 21:47:57.837 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:47:57.837 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:47:58.076 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive 192.168.123.182:28183 register finished
2025-07-16 21:47:58.682 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [192.168.10.104:5672]
2025-07-16 21:47:58.770 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#486d1868:0/SimpleConnection@40a2cae4 [delegate=amqp://admin@192.168.10.104:5672/, localPort= 52330]
2025-07-16 21:47:58.923 INFO  [main] c.h.AppMain - Started AppMain in 15.22 seconds (JVM running for 16.944)
2025-07-16 21:47:58.924 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:47:58.925 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:47:58.925 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-14 00:00:00_2025-07-18 00:00:00\",\"title\":\"五勤务群奥二无\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-14 00:00:00_2025-07-18 00:00:00\",\"qjsy\":\"五勤务群奥二无\",\"sflc\":\"是\",\"qjwcdd\":[\"11\"],\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:15:28\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:15:29","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"565b5538-423a-4662-ac2e-c30d93ec6bfb"}
2025-07-16 21:47:58.973 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-14 00:00:00_2025-07-18 00:00:00\",\"title\":\"五勤务群奥二无\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-14 00:00:00_2025-07-18 00:00:00\",\"qjsy\":\"五勤务群奥二无\",\"sflc\":\"是\",\"qjwcdd\":[\"11\"],\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:15:36\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:15:36","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"214a6288-25b4-46a6-8825-3fbf227a2fd6"}
2025-07-16 21:47:58.974 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-14 00:00:00_2025-07-18 00:00:00\",\"title\":\"五勤务群奥二无\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-14 00:00:00_2025-07-18 00:00:00\",\"qjsy\":\"五勤务群奥二无\",\"sflc\":\"是\",\"qjwcdd\":[\"11\"],\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:15:44\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:15:44","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"ede89aaf-24ce-4e4e-9861-28bff090e929"}
2025-07-16 21:47:58.974 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-15 00:00:00_2025-07-18 00:00:00\",\"title\":\"威尔而\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-15 00:00:00_2025-07-18 00:00:00\",\"qjsy\":\"威尔而\",\"sflc\":\"是\",\"qjwcdd\":[\"11\"],\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:18:01\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:18:01","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"50e8b610-6046-477c-a4e6-6a8de43211cd"}
2025-07-16 21:47:58.974 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-11 00:00:00_2025-08-13 00:00:00\",\"title\":\"11\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"科级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":[\"320421197906181814\"],\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-11 00:00:00_2025-08-13 00:00:00\",\"qjsy\":\"11\",\"sflc\":\"否\",\"qjwcdd\":[\"11\"],\"zwjb\":\"科级干部\",\"qjlsdzry\":[\"320421197906181814\"]}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:18:10\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:18:10","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"279b4c58-9a0f-4be8-a24c-7efb431a190a"}
2025-07-16 21:47:58.974 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-11 00:00:00_2025-08-13 00:00:00\",\"title\":\"11\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"12\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"科级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":[\"320421197906181814\"],\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-11 00:00:00_2025-08-13 00:00:00\",\"qjsy\":\"11\",\"sflc\":\"否\",\"qjwcdd\":[\"12\"],\"zwjb\":\"科级干部\",\"qjlsdzry\":[\"320421197906181814\"]}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:18:29\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:18:29","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"872c73da-d713-41a5-b4c6-33bd97414e6f"}
2025-07-16 21:47:58.975 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-09 00:00:00_2025-07-10 00:00:00\",\"title\":\"电放费\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-09 00:00:00_2025-07-10 00:00:00\",\"qjsy\":\"电放费\",\"sflc\":\"否\",\"qjwcdd\":\"\",\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:19:09\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:19:09","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"076d8c80-7f8a-4b58-be11-0f61b072738f"}
2025-07-16 21:47:58.975 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-10 00:00:00_2025-08-04 00:00:00\",\"title\":\"22\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"12\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"科级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":[\"320421197906181814\"],\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-10 00:00:00_2025-08-04 00:00:00\",\"qjsy\":\"22\",\"sflc\":\"是\",\"qjwcdd\":[\"12\"],\"zwjb\":\"科级干部\",\"qjlsdzry\":[\"320421197906181814\"]}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 16:21:12\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:21:12","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"6384b681-0f38-467e-af59-9a0afd65b020"}
2025-07-16 21:47:58.975 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-15 00:00:00_2025-07-16 00:00:00\",\"title\":\"大幅度\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-15 00:00:00_2025-07-16 00:00:00\",\"qjsy\":\"大幅度\",\"sflc\":\"否\",\"qjwcdd\":\"\",\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:21:45\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:21:45","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"61c20cd1-1f65-42b5-8ee6-d18a7932ebfd"}
2025-07-16 21:47:58.975 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-15 00:00:00_2025-07-16 00:00:00\",\"title\":\"大幅度\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-15 00:00:00_2025-07-16 00:00:00\",\"qjsy\":\"大幅度\",\"sflc\":\"否\",\"qjwcdd\":\"\",\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:21:54\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:21:54","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"0f3ebbf0-05b7-4b9d-a976-5b9aeb905edf"}
2025-07-16 21:47:58.976 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-15 00:00:00_2025-07-18 00:00:00\",\"title\":\"444\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-15 00:00:00_2025-07-18 00:00:00\",\"qjsy\":\"444\",\"sflc\":\"否\",\"qjwcdd\":\"\",\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:24:40\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:24:40","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"f61aab38-5c74-4e66-a2e6-2ad723004d9b"}
2025-07-16 21:47:58.976 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-15 00:00:00_2025-07-18 00:00:00\",\"title\":\"444\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\",\"12\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-15 00:00:00_2025-07-18 00:00:00\",\"qjsy\":\"444\",\"sflc\":\"是\",\"qjwcdd\":[\"11\",\"12\"],\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:25:54\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:25:54","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"71c3ff37-6cdb-4522-9fac-7f2319c266d6"}
2025-07-16 21:47:58.976 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623684455300374\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-17 00:00:00_2025-07-19 00:00:00\",\"title\":\"45\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\",\"12\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623684455300374\",\"qjkssj\":\"2025-07-17 00:00:00_2025-07-19 00:00:00\",\"qjsy\":\"45\",\"sflc\":\"是\",\"qjwcdd\":[\"11\",\"12\"],\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:27:21\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:27:21","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"0ceee906-f361-497a-b761-dfa373b8ec0b"}
2025-07-16 21:47:58.976 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_055619\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"政治教导员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-10 00:00:00_2025-08-04 00:00:00\",\"title\":\"22\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"科级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":[\"320421197906181814\"],\"sign_url\":\"M_055619\",\"xm\":\"M_055619\",\"dw\":\"************\",\"zw\":\"政治教导员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-10 00:00:00_2025-08-04 00:00:00\",\"qjsy\":\"22\",\"sflc\":\"是\",\"qjwcdd\":[\"11\"],\"zwjb\":\"科级干部\",\"qjlsdzry\":[\"320421197906181814\"]}],\"type\":\"check\",\"id_card\":\"320421197906181814\",\"time\":\"2025-07-15 16:27:22\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:27:22","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"e63a72b6-ba40-4e3b-8d57-e099a209f42a"}
2025-07-16 21:47:58.977 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-18 00:00:00_2025-08-11 00:00:00\",\"title\":\"56\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_MjilpLpxAMnUdWlF\":[\"11\",\"12\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"股级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-18 00:00:00_2025-08-11 00:00:00\",\"qjsy\":\"56\",\"sflc\":\"是\",\"qjwcdd\":[\"11\",\"12\"],\"zwjb\":\"股级干部\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:31:32\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:31:32","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"5f7f0c63-a621-4995-a657-01e9e0bac66b"}
2025-07-16 21:47:58.977 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_055619\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"政治教导员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-10 00:00:00_2025-08-05 00:00:00\",\"title\":\"22\",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"股级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_055619\",\"xm\":\"M_055619\",\"dw\":\"************\",\"zw\":\"政治教导员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-10 00:00:00_2025-08-05 00:00:00\",\"qjsy\":\"22\",\"sflc\":\"是\",\"zwjb\":\"股级干部\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320421197906181814\",\"time\":\"2025-07-15 16:32:09\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 16:32:09","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"254d0ec0-636a-40bd-91b8-6d7f07a25d90"}
2025-07-16 21:47:58.978 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752549550401G1XL0\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:32:32\"}}","content_time":"2025-07-15 16:32:32","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"807d4af8-538e-450b-bfb2-db92ac8217e7"}
2025-07-16 21:47:58.978 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752549838680NAJWO\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:32:36\"}}","content_time":"2025-07-15 16:32:36","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"5285839f-d17a-4c14-867e-57f674b39883"}
2025-07-16 21:47:58.978 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752548999855XL2LJ\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:33:08\"}}","content_time":"2025-07-15 16:33:08","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"17be5c18-4e63-4fec-837e-ba87a2df70b0"}
2025-07-16 21:47:58.978 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"17522186263762T2FB\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:38:00\"}}","content_time":"2025-07-15 16:38:01","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"ab146bf0-2d93-44dd-be7d-073ad7515fa6"}
2025-07-16 21:47:58.978 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752546627681E0SAB\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:38:06\"}}","content_time":"2025-07-15 16:38:07","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"65900274-6c2b-4fbe-9d9b-474daa1e05e8"}
2025-07-16 21:47:58.979 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752219063950D4CZ5\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:39:12\"}}","content_time":"2025-07-15 16:39:12","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"6ac1eae1-e777-4631-ae99-81c372760ba8"}
2025-07-16 21:47:58.979 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752219122416SCUT8\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:39:16\"}}","content_time":"2025-07-15 16:39:16","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"5863f6be-9f13-4c86-a965-3db03050c6d4"}
2025-07-16 21:47:58.979 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752484847671KY2NR\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:39:54\"}}","content_time":"2025-07-15 16:39:54","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"e2156917-aee3-4d71-8398-7e02bd690b21"}
2025-07-16 21:47:58.979 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752545678838MQECF\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:39:58\"}}","content_time":"2025-07-15 16:39:58","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"162f5721-696b-4065-a113-825d804153a5"}
2025-07-16 21:47:58.979 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"17525463565757KIA1\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:40:02\"}}","content_time":"2025-07-15 16:40:02","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"7953f214-4d59-4310-877c-5fa5dfbceacb"}
2025-07-16 21:47:58.980 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752547282856OUK03\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:40:06\"}}","content_time":"2025-07-15 16:40:06","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"1da34821-0a78-441d-9aac-c715d1018991"}
2025-07-16 21:47:58.980 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752548692881PRQ1N\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:40:10\"}}","content_time":"2025-07-15 16:40:10","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"36311587-8d80-46ec-9ca6-90140af90ea9"}
2025-07-16 21:47:58.980 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752548749117B1T0S\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:40:14\"}}","content_time":"2025-07-15 16:40:14","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"406bc09e-dd2c-4f14-9de3-e10d19ed5eea"}
2025-07-16 21:47:58.980 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"delete\",\"data\":{\"task_id\":\"1752549082095TFHZL\",\"config_id\":\"TC1752201774502MOX_WJ\",\"config_uuid\":\"C1FI31VEE7X\",\"type\":\"delete\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 16:40:18\"}}","content_time":"2025-07-15 16:40:18","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"3e35cd1a-171d-4c5b-8946-ba87253b18c7"}
2025-07-16 21:47:58.980 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_hualong\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"民警\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-18 00:00:00_2025-07-25 00:00:00\",\"title\":\"仍然\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_TdNNLQat48_c6yRB\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"普通民警\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-18 00:00:00_2025-07-25 00:00:00\",\"qjsy\":\"仍然\",\"sflc\":\"否\",\"zwjb\":\"普通民警\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"hualong\",\"time\":\"2025-07-15 17:06:10\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 17:06:10","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"680eafe4-003e-4e22-bdc5-7c17f8fd048a"}
2025-07-16 21:47:58.981 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_055619\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"政治教导员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-01 00:00:00_2025-08-06 00:00:00\",\"title\":\"2\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_TdNNLQat48_c6yRB\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"股级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_055619\",\"xm\":\"M_055619\",\"dw\":\"************\",\"zw\":\"政治教导员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-07-01 00:00:00_2025-08-06 00:00:00\",\"qjsy\":\"2\",\"sflc\":\"否\",\"zwjb\":\"股级干部\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320421197906181814\",\"time\":\"2025-07-15 17:28:24\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 17:28:24","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"8193a470-c3cd-4376-af7b-4b9b86c3faee"}
2025-07-16 21:47:58.981 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_055619\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"政治教导员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623684455300374\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-07-03 00:00:00_2025-08-09 00:00:00\",\"title\":\"22\",\"FormSelect_zrRKV8805AOE4Jn3\":\"否\",\"FormCascaderSelect_TdNNLQat48_c6yRB\":\"\",\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"股级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_055619\",\"xm\":\"M_055619\",\"dw\":\"************\",\"zw\":\"政治教导员\",\"qjlb\":\"17519623684455300374\",\"qjkssj\":\"2025-07-03 00:00:00_2025-08-09 00:00:00\",\"qjsy\":\"22\",\"sflc\":\"否\",\"zwjb\":\"股级干部\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320421197906181814\",\"time\":\"2025-07-15 17:35:25\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 17:35:25","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"22de8b9b-cf05-4284-b550-c0a529ee31fa"}
2025-07-16 21:47:58.981 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.LeaveRecordListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"check\",\"data\":{\"content\":[{\"config_uuid\":\"C1FI31VEE7X\",\"FormPersonName_xA9sEt12LVRaGE78\":\"M_057177\",\"FormPersonOrg_JKSdTMFEGPxznRSM\":\"************\",\"FormPersonJob_nSlu3KTlO5G1fGOX\":\"法制员\",\"FormCascaderSelect_DVdi6ySndE4Ng7Yt\":\"17519623382069508772\",\"FormDate_9uPUSsUjyP_16UtB\":\"2025-08-12 00:00:00_2025-08-17 00:00:00\",\"title\":\"23232323 \",\"FormSelect_zrRKV8805AOE4Jn3\":\"是\",\"FormCascaderSelect_TdNNLQat48_c6yRB\":[\"11\",\"12\"],\"FormSelect_Z9L5O2_VvSlsy0aZ\":\"股级干部\",\"FormPersonSelect_V92ZTYhsrnX0h_B0\":\"\",\"sign_url\":\"M_057177\",\"xm\":\"M_057177\",\"dw\":\"************\",\"zw\":\"法制员\",\"qjlb\":\"17519623382069508772\",\"qjkssj\":\"2025-08-12 00:00:00_2025-08-17 00:00:00\",\"qjsy\":\"23232323 \",\"sflc\":\"是\",\"zwjb\":\"股级干部\",\"qjlsdzry\":\"\"}],\"type\":\"check\",\"id_card\":\"320483199311161513\",\"time\":\"2025-07-15 17:53:49\",\"config_uuid\":\"C1FI31VEE7X\",\"config_id\":\"TC175256358881380W_WJ\"}}","content_time":"2025-07-15 17:53:49","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"4e82359b-deba-4e9e-ad5b-68349e83624a"}
2025-07-16 21:48:02.403 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 2025-07-16 08:48:53
2025-07-16 21:48:02.410 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-07-16 21:48:02.410 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-07-16 21:48:02.411 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-07-16 21:48:02.422 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-07-16 21:48:02.551 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-07-16 21:48:02.620 INFO  [RMI TCP Connection(3)-192.168.123.182] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 21:48:02.620 INFO  [RMI TCP Connection(3)-192.168.123.182] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 21:48:02.625 INFO  [RMI TCP Connection(3)-192.168.123.182] o.s.w.s.DispatcherServlet - Completed initialization in 5 ms
2025-07-16 21:48:02.800 WARN  [RMI TCP Connection(6)-192.168.123.182] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_452]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_452]
	at sun.reflect.GeneratedMethodAccessor67.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-07-16 21:48:04.248 INFO  [main] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-07-16 21:53:11.202 WARN  [Thread-10] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-16 21:53:11.202 WARN  [Thread-5] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-16 21:53:11.203 WARN  [Thread-10] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-16 21:53:11.203 WARN  [Thread-5] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-16 21:53:11.220 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-16 21:53:11.221 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-16 21:53:12.205 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-07-16 21:53:15.228 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-07-16 21:53:15.246 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 21:53:15.256 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 21:53:15.441 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-07-16 21:53:15.446 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-07-16 21:53:15.486 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
2025-07-16 21:56:26.462 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-16 21:56:26.490 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-16 21:56:26.814 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:56:26.814 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:56:27.763 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-07-16 21:56:27.774 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-07-16 21:56:27.784 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-07-16 21:56:27.784 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-07-16 21:56:27.802 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-07-16 21:56:28.463 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-16 21:56:28.466 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-16 21:56:28.486 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Elasticsearch repository interfaces.
2025-07-16 21:56:28.489 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-16 21:56:28.489 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-16 21:56:28.502 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-16 21:56:28.511 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-16 21:56:28.512 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-16 21:56:28.521 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-16 21:56:28.654 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:56:28.654 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-07-16 21:56:28.833 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=7ffefe6d-bc56-315f-af6d-6c06d983e88e
2025-07-16 21:56:29.203 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:56:29.207 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:56:29.208 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$510/533353383] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:56:29.213 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:56:29.286 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$5157ba03] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:56:29.288 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$aee41808] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-16 21:56:29.658 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-07-16 21:56:29.669 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-07-16 21:56:29.671 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-07-16 21:56:29.671 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-16 21:56:29.763 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 21:56:29.763 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1951 ms
2025-07-16 21:56:29.919 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-07-16 21:56:30.234 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-07-16 21:56:31.091 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-07-16 21:56:31.092 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.096 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-07-16 21:56:31.096 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.100 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-07-16 21:56:31.100 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.104 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-07-16 21:56:31.104 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.108 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-07-16 21:56:31.108 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.112 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-07-16 21:56:31.112 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.116 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-07-16 21:56:31.116 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.120 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-07-16 21:56:31.120 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.124 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-07-16 21:56:31.124 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.128 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-07-16 21:56:31.128 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.133 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-07-16 21:56:31.133 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.138 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-07-16 21:56:31.138 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.143 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-07-16 21:56:31.143 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.147 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-07-16 21:56:31.147 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.152 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-07-16 21:56:31.152 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.158 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-07-16 21:56:31.158 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.162 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-07-16 21:56:31.163 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.172 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-07-16 21:56:31.172 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.176 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-07-16 21:56:31.176 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.181 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-07-16 21:56:31.181 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.185 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-07-16 21:56:31.185 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.188 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-07-16 21:56:31.188 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.192 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-07-16 21:56:31.192 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.196 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-07-16 21:56:31.196 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.200 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-07-16 21:56:31.200 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.203 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-07-16 21:56:31.204 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.207 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-07-16 21:56:31.207 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.211 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-07-16 21:56:31.211 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.214 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-07-16 21:56:31.214 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.218 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-07-16 21:56:31.218 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.222 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-07-16 21:56:31.222 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.225 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-07-16 21:56:31.225 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.229 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-07-16 21:56:31.229 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.234 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-07-16 21:56:31.234 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.238 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-07-16 21:56:31.238 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.242 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-07-16 21:56:31.242 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.246 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-07-16 21:56:31.246 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.250 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-07-16 21:56:31.250 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.254 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-07-16 21:56:31.254 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.260 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-07-16 21:56:31.260 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.267 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-07-16 21:56:31.267 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.273 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-07-16 21:56:31.273 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.277 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-07-16 21:56:31.278 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:31.282 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-07-16 21:56:31.282 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-16 21:56:32.810 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-07-16 21:56:33.130 INFO  [main] easy-es - ===> manual index mode activated
2025-07-16 21:56:33.164 INFO  [main] easy-es - ===> manual index mode activated
2025-07-16 21:56:35.857 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:56:35.857 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:56:36.218 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"192.168.10.104:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-07-16 21:56:37.941 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-16 21:56:38.233 INFO  [main] c.h.s.c.SecurityConfig - [/dict/api-query, /test/dict, /dict-test/test, /dict/add-batch, /error/logs] --> 200
2025-07-16 21:56:38.300 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7631fb3e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3aede2ff, org.springframework.security.web.context.SecurityContextPersistenceFilter@7752f9fe, org.springframework.security.web.header.HeaderWriterFilter@24afdf18, org.springframework.security.web.authentication.logout.LogoutFilter@7ecf001f, org.springframework.web.filter.CorsFilter@ab5c335, com.hl.security.config.sso.SsoAuthTokenFilter@6cd6698b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6351efa6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@68fd0fba, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5769679b, org.springframework.security.web.session.SessionManagementFilter@3d28c0af, org.springframework.security.web.access.ExceptionTranslationFilter@25000d3d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@a6f34f5]
2025-07-16 21:56:39.904 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-07-16 21:56:39.924 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-07-16 21:56:39.928 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:56:39.928 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:56:40.161 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive 192.168.123.182:28183 register finished
2025-07-16 21:56:40.748 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [192.168.10.104:5672]
2025-07-16 21:56:40.842 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#759862a2:0/SimpleConnection@6183dd2 [delegate=amqp://admin@192.168.10.104:5672/, localPort= 52581]
2025-07-16 21:56:40.976 INFO  [main] c.h.AppMain - Started AppMain in 14.715 seconds (JVM running for 15.471)
2025-07-16 21:56:40.977 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-16 21:56:40.977 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-16 21:56:44.325 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-07-16 21:56:44.332 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-07-16 21:56:44.332 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-07-16 21:56:44.333 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-07-16 21:56:44.348 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-07-16 21:56:44.466 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-07-16 21:56:44.717 INFO  [RMI TCP Connection(1)-192.168.123.182] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 21:56:44.717 INFO  [RMI TCP Connection(1)-192.168.123.182] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 21:56:44.727 INFO  [RMI TCP Connection(1)-192.168.123.182] o.s.w.s.DispatcherServlet - Completed initialization in 10 ms
2025-07-16 21:56:44.911 WARN  [RMI TCP Connection(3)-192.168.123.182] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Connection refused
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_452]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_452]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_452]
	at sun.reflect.GeneratedMethodAccessor55.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_452]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_452]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-07-16 21:56:46.033 INFO  [main] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-07-16 21:56:48.180 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-16 21:56:48.180 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-16 21:56:48.180 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-16 21:56:48.181 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-16 21:56:48.198 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-07-16 21:56:48.198 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-16 21:56:49.022 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-07-16 21:56:52.210 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-07-16 21:56:52.235 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 21:56:52.247 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 21:56:52.438 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-07-16 21:56:52.442 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-07-16 21:56:52.486 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
2025-08-01 23:33:53.128 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-01 23:33:53.156 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-01 23:33:53.506 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 23:33:53.507 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 23:33:54.777 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-01 23:33:54.814 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-01 23:33:54.848 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-01 23:33:54.849 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-01 23:33:54.872 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-01 23:33:55.614 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 23:33:55.617 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-01 23:33:55.640 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Elasticsearch repository interfaces.
2025-08-01 23:33:55.643 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 23:33:55.644 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-01 23:33:55.651 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-01 23:33:55.661 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 23:33:55.662 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 23:33:55.673 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewArchiveCaseTaskInfoDocumentMapper' and 'com.hl.archive.search.mapper.ViewArchiveCaseTaskInfoDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:55.846 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-01 23:33:56.059 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=84ae4dfa-a2e2-3576-b4a7-1fa54676b495
2025-08-01 23:33:56.505 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 23:33:56.509 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 23:33:56.510 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$510/979620424] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 23:33:56.516 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 23:33:56.604 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$5b98830b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 23:33:56.606 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$b924e110] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 23:33:57.031 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-01 23:33:57.040 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-01 23:33:57.042 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-01 23:33:57.042 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-01 23:33:57.146 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 23:33:57.146 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2262 ms
2025-08-01 23:33:57.330 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 23:33:57.654 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-01 23:33:58.712 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-01 23:33:58.712 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.717 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-01 23:33:58.717 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.722 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-01 23:33:58.722 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.727 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-01 23:33:58.727 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.732 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-01 23:33:58.732 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.737 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-01 23:33:58.737 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.742 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-01 23:33:58.742 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.747 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-01 23:33:58.748 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.753 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-01 23:33:58.753 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.765 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-01 23:33:58.765 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.770 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-01 23:33:58.770 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.775 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-01 23:33:58.775 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.780 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-01 23:33:58.780 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.784 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-01 23:33:58.785 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.790 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-01 23:33:58.790 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.794 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-01 23:33:58.794 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.798 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-01 23:33:58.799 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.802 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-01 23:33:58.802 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.806 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-01 23:33:58.806 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.811 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-01 23:33:58.812 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.816 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-01 23:33:58.816 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.821 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-01 23:33:58.821 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.825 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-01 23:33:58.825 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.829 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-01 23:33:58.830 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.834 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-01 23:33:58.834 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.838 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-01 23:33:58.838 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.842 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-01 23:33:58.842 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.846 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-01 23:33:58.846 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.850 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-01 23:33:58.850 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.854 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-01 23:33:58.854 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.858 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-01 23:33:58.858 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.862 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-01 23:33:58.862 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.867 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-01 23:33:58.867 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.872 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-01 23:33:58.872 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.877 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-01 23:33:58.877 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.881 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-01 23:33:58.881 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.885 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-01 23:33:58.885 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.889 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-01 23:33:58.889 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.894 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-01 23:33:58.894 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.900 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-01 23:33:58.900 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.907 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-01 23:33:58.907 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.914 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-01 23:33:58.914 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.919 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-01 23:33:58.919 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:33:58.925 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-01 23:33:58.925 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 23:34:01.375 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 23:34:01.794 INFO  [main] easy-es - ===> manual index mode activated
2025-08-01 23:34:01.817 INFO  [main] easy-es - ===> manual index mode activated
2025-08-01 23:34:01.990 INFO  [main] easy-es - ===> manual index mode activated
2025-08-01 23:34:02.036 INFO  [main] easy-es - ===> manual index mode activated
2025-08-01 23:34:04.347 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 23:34:04.347 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 23:34:04.854 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"192.168.10.104:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-01 23:34:06.939 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 23:34:07.285 INFO  [main] c.h.s.c.SecurityConfig - [/error/logs, /dict-test/test, /test/dict, /dict/add-batch, /dict/api-query] --> 200
2025-08-01 23:34:07.360 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3c82bcbd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@776ae580, org.springframework.security.web.context.SecurityContextPersistenceFilter@795d43d9, org.springframework.security.web.header.HeaderWriterFilter@1f9af742, org.springframework.security.web.authentication.logout.LogoutFilter@547d3e8e, org.springframework.web.filter.CorsFilter@6b0bcea5, com.hl.security.config.sso.SsoAuthTokenFilter@16361e61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b3b25ad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@76a41af0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5748638c, org.springframework.security.web.session.SessionManagementFilter@6af447b6, org.springframework.security.web.access.ExceptionTranslationFilter@4bba628, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@bf70ce5]
2025-08-01 23:34:09.239 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-01 23:34:09.263 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-01 23:34:09.275 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 23:34:09.275 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 23:34:09.667 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive 192.168.123.116:28183 register finished
2025-08-01 23:34:10.402 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [192.168.10.104:5672]
2025-08-01 23:34:10.623 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#7f289126:0/SimpleConnection@649573d0 [delegate=amqp://admin@192.168.10.104:5672/, localPort= 65425]
2025-08-01 23:34:10.899 INFO  [main] c.h.AppMain - Started AppMain in 17.992 seconds (JVM running for 18.953)
2025-08-01 23:34:10.901 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 23:34:10.901 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 23:34:35.729 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 23:34:35.729 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-01 23:34:35.729 WARN  [Thread-9] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-01 23:34:35.729 WARN  [Thread-3] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-01 23:34:36.099 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-01 23:34:36.102 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01 23:34:36.458 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
