package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrGrjd;
import com.hl.orasync.domain.VWjBrGrjdToPoliceLoanInfoMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,VWjBrGrjdToPoliceLoanInfoMapper.class},
    imports = {}
)
public interface PoliceLoanInfoToVWjBrGrjdMapper extends BaseMapper<PoliceLoanInfo, VWjBrGrjd> {
  @Mapping(
      target = "jdyt",
      source = "loanPurpose"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jdrq",
      source = "loanDate"
  )
  @Mapping(
      target = "jdlxmc",
      source = "loanInfo"
  )
  @Mapping(
      target = "hkqx",
      source = "repaymentDeadline"
  )
  @Mapping(
      target = "je",
      source = "loanAmount"
  )
  @Mapping(
      target = "xmJddx",
      source = "lenderName"
  )
  VWjBrGrjd convert(PoliceLoanInfo source);

  @Mapping(
      target = "jdyt",
      source = "loanPurpose"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jdrq",
      source = "loanDate"
  )
  @Mapping(
      target = "jdlxmc",
      source = "loanInfo"
  )
  @Mapping(
      target = "hkqx",
      source = "repaymentDeadline"
  )
  @Mapping(
      target = "je",
      source = "loanAmount"
  )
  @Mapping(
      target = "xmJddx",
      source = "lenderName"
  )
  VWjBrGrjd convert(PoliceLoanInfo source, @MappingTarget VWjBrGrjd target);
}
