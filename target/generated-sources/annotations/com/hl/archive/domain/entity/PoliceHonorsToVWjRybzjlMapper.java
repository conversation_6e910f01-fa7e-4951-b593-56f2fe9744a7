package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBzjlGrryToPoliceHonorsMapper;
import com.hl.orasync.domain.VWjRybzjl;
import com.hl.orasync.domain.VWjRybzjlToPoliceHonorsMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,VWjBzjlGrryToPoliceHonorsMapper.class,VWjRybzjlToPoliceHonorsMapper.class,PoliceHonorsToVWjBzjlGrryMapper.class},
    imports = {}
)
public interface PoliceHonorsToVWjRybzjlMapper extends BaseMapper<PoliceHonors, VWjRybzjl> {
  @Mapping(
      target = "jcsj",
      source = "awardDate"
  )
  @Mapping(
      target = "bh",
      source = "bh"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jcpjjgmc",
      source = "approveAuthority"
  )
  @Mapping(
      target = "jcmc",
      source = "honorName"
  )
  VWjRybzjl convert(PoliceHonors source);

  @Mapping(
      target = "jcsj",
      source = "awardDate"
  )
  @Mapping(
      target = "bh",
      source = "bh"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jcpjjgmc",
      source = "approveAuthority"
  )
  @Mapping(
      target = "jcmc",
      source = "honorName"
  )
  VWjRybzjl convert(PoliceHonors source, @MappingTarget VWjRybzjl target);
}
