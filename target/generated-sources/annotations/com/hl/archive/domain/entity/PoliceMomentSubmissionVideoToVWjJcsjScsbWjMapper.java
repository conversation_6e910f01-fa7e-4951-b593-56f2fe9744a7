package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjJcsjScsbWj;
import com.hl.orasync.domain.VWjJcsjScsbWjToPoliceMomentSubmissionVideoMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,VWjJcsjScsbWjToPoliceMomentSubmissionVideoMapper.class},
    imports = {}
)
public interface PoliceMomentSubmissionVideoToVWjJcsjScsbWjMapper extends BaseMapper<PoliceMomentSubmissionVideo, VWjJcsjScsbWj> {
  @Mapping(
      target = "fjcl2",
      source = "fileUrl"
  )
  @Mapping(
      target = "wjlxmc",
      source = "fileType"
  )
  @Mapping(
      target = "sbXxzjbh",
      source = "sbZjbh"
  )
  @Mapping(
      target = "wjmc",
      source = "fileName"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjJcsjScsbWj convert(PoliceMomentSubmissionVideo source);

  @Mapping(
      target = "fjcl2",
      source = "fileUrl"
  )
  @Mapping(
      target = "wjlxmc",
      source = "fileType"
  )
  @Mapping(
      target = "sbXxzjbh",
      source = "sbZjbh"
  )
  @Mapping(
      target = "wjmc",
      source = "fileName"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjJcsjScsbWj convert(PoliceMomentSubmissionVideo source, @MappingTarget VWjJcsjScsbWj target);
}
