package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrGatwlqk;
import com.hl.orasync.domain.VWjBrGatwlqkToPoliceHkMacauTaiwanTravelMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,VWjBrGatwlqkToPoliceHkMacauTaiwanTravelMapper.class},
    imports = {}
)
public interface PoliceHkMacauTaiwanTravelToVWjBrGatwlqkMapper extends BaseMapper<PoliceHkMacauTaiwanTravel, VWjBrGatwlqk> {
  @Mapping(
      target = "jssj",
      source = "endDate"
  )
  @Mapping(
      target = "sdgj",
      source = "destinationRegion"
  )
  @Mapping(
      target = "sy",
      source = "travelReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "spjgmc",
      source = "approvalAuthority"
  )
  @Mapping(
      target = "zjhm",
      source = "documentNumber"
  )
  @Mapping(
      target = "kssj",
      source = "startDate"
  )
  VWjBrGatwlqk convert(PoliceHkMacauTaiwanTravel source);

  @Mapping(
      target = "jssj",
      source = "endDate"
  )
  @Mapping(
      target = "sdgj",
      source = "destinationRegion"
  )
  @Mapping(
      target = "sy",
      source = "travelReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "spjgmc",
      source = "approvalAuthority"
  )
  @Mapping(
      target = "zjhm",
      source = "documentNumber"
  )
  @Mapping(
      target = "kssj",
      source = "startDate"
  )
  VWjBrGatwlqk convert(PoliceHkMacauTaiwanTravel source, @MappingTarget VWjBrGatwlqk target);
}
