package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyzzmm;
import com.hl.orasync.domain.VWjRyzzmmToPolicePoliticalStatusMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,VWjRyzzmmToPolicePoliticalStatusMapper.class},
    imports = {}
)
public interface PolicePoliticalStatusToVWjRyzzmmMapper extends BaseMapper<PolicePoliticalStatus, VWjRyzzmm> {
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zzsf",
      source = "politicalIdentity"
  )
  @Mapping(
      target = "cjdpsj",
      source = "joinPartyDate"
  )
  VWjRyzzmm convert(PolicePoliticalStatus source);

  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zzsf",
      source = "politicalIdentity"
  )
  @Mapping(
      target = "cjdpsj",
      source = "joinPartyDate"
  )
  VWjRyzzmm convert(PolicePoliticalStatus source, @MappingTarget VWjRyzzmm target);
}
