package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtClxx;
import com.hl.orasync.domain.VWjQtClxxToPoliceFamilyVehiclesMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,VWjQtClxxToPoliceFamilyVehiclesMapper.class},
    imports = {}
)
public interface PoliceFamilyVehiclesToVWjQtClxxMapper extends BaseMapper<PoliceFamilyVehicles, VWjQtClxx> {
  @Mapping(
      target = "clqxmc",
      source = "vehicleDisposition"
  )
  @Mapping(
      target = "xmCqr",
      source = "ownerName"
  )
  @Mapping(
      target = "csjg",
      source = "saleAmount"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "clpp",
      source = "vehicleBrand"
  )
  @Mapping(
      target = "cllymc",
      source = "vehicleSource"
  )
  @Mapping(
      target = "cssj",
      source = "saleDate"
  )
  @Mapping(
      target = "je",
      source = "transactionAmount"
  )
  @Mapping(
      target = "gmsj",
      source = "transactionDate"
  )
  @Mapping(
      target = "hphm",
      source = "licensePlate"
  )
  VWjQtClxx convert(PoliceFamilyVehicles source);

  @Mapping(
      target = "clqxmc",
      source = "vehicleDisposition"
  )
  @Mapping(
      target = "xmCqr",
      source = "ownerName"
  )
  @Mapping(
      target = "csjg",
      source = "saleAmount"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "clpp",
      source = "vehicleBrand"
  )
  @Mapping(
      target = "cllymc",
      source = "vehicleSource"
  )
  @Mapping(
      target = "cssj",
      source = "saleDate"
  )
  @Mapping(
      target = "je",
      source = "transactionAmount"
  )
  @Mapping(
      target = "gmsj",
      source = "transactionDate"
  )
  @Mapping(
      target = "hphm",
      source = "licensePlate"
  )
  VWjQtClxx convert(PoliceFamilyVehicles source, @MappingTarget VWjQtClxx target);
}
