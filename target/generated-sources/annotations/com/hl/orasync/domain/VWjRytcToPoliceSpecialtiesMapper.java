package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceSpecialties;
import com.hl.archive.domain.entity.PoliceSpecialtiesToVWjRytcMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__6;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__6.class,
    uses = {ConversionUtils.class,PoliceSpecialtiesToVWjRytcMapper.class},
    imports = {}
)
public interface VWjRytcToPoliceSpecialtiesMapper extends BaseMapper<VWjRytc, PoliceSpecialties> {
  @Mapping(
      target = "approveAuthority",
      source = "jcpjjgmc"
  )
  @Mapping(
      target = "specialtyName",
      source = "jcmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "awardDate",
      source = "jcsj",
      qualifiedByName = {"strToDate"}
  )
  PoliceSpecialties convert(VWjRytc source);

  @Mapping(
      target = "approveAuthority",
      source = "jcpjjgmc"
  )
  @Mapping(
      target = "specialtyName",
      source = "jcmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "awardDate",
      source = "jcsj",
      qualifiedByName = {"strToDate"}
  )
  PoliceSpecialties convert(VWjRytc source, @MappingTarget PoliceSpecialties target);
}
